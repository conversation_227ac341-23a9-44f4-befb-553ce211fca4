/* Modern Theme System for Wegweiser
   Simplified to support Light and Dark themes only
*/

/* Light Theme (Default) - Enhanced with nicestyle.css colors and glassmorphism */
[data-bs-theme="light"] {
    /* Colors - using nicestyle.css high-contrast palette */
    --primary: #0078d4;
    --primary-rgb: 0, 120, 212;
    --primary-dark: #005ba4;
    --secondary: #5B6B86;
    --success: #06A6A9;
    --danger: #E83C4B;
    --warning: #FFAB00;
    --info: #39D0D8;

    /* Background colors - glassmorphism enhanced */
    --body-bg: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    --card-bg: rgba(255, 255, 255, 0.25);
    --sidebar-bg: rgba(255, 255, 255, 0.35);
    --header-bg: rgba(255, 255, 255, 0.35);

    /* Text colors with enhanced contrast from nicestyle.css */
    --text-primary: #333333;
    --text-secondary: #242424;
    --text-muted: #475569;

    /* Add accent text colors for emphasis */
    --text-accent: #005fb8;
    --text-success: #05959A;
    --text-danger: #D32F3A;

    /* Border colors - glassmorphism enhanced */
    --border-color: rgba(255, 255, 255, 0.18);
    --border-light: rgba(255, 255, 255, 0.12);

    /* Glassmorphism specific variables for light theme */
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-bg-hover: rgba(255, 255, 255, 0.35);
    --glass-border: rgba(255, 255, 255, 0.18);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    --glass-blur: blur(10px);
    --glass-border-radius: 10px;

    /* Notification colors */
    --notification-info-bg: rgba(59, 208, 216, 0.15);
    --notification-success-bg: rgba(6, 166, 169, 0.15);
    --notification-warning-bg: rgba(255, 171, 0, 0.15);
    --notification-danger-bg: rgba(232, 60, 75, 0.15);

    /* Enhanced gradient for light theme with glassmorphism backdrop */
    --main-gradient: linear-gradient(135deg, rgba(40, 81, 231, 0.08) 0%, rgba(6, 166, 169, 0.12) 50%, rgba(255, 255, 255, 0.05) 100%);
    --backdrop-pattern: radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
                       radial-gradient(circle at 80% 20%, rgba(255, 171, 0, 0.08) 0%, transparent 50%),
                       radial-gradient(circle at 40% 80%, rgba(6, 166, 169, 0.06) 0%, transparent 50%);
}

/* Added specific styles for sidebar navigation in light theme */
[data-bs-theme="light"] .sidebar-nav .nav-link {
    color: #1A1D21;
    /* Black text for menu links */
}

[data-bs-theme="light"] .sidebar-nav .nav-link.active {
    color: #1A1D21;
    /* Keep active links black too */
    background-color: rgba(171, 168, 69, 0.15);
    /* Light gold background */
    font-weight: 500;
}

[data-bs-theme="light"] .sidebar-nav .nav-link:hover {
    background-color: rgba(171, 168, 69, 0.1);
    color: #1A1D21;
    /* Keep hover state black as well */
}

/* Dark Theme - Enhanced with nicestyle.css contrast colors and glassmorphism */
[data-bs-theme="dark"] {
    /* Colors - using nicestyle.css high-contrast palette */
    --primary: #0078d4;
    --primary-rgb: 0, 120, 212;
    --primary-dark: #005ba4;
    --secondary: #9ba3b4;
    --success: #0ECFD3;
    --danger: #FF5A69;
    --warning: #FFB930;
    --info: #4daafc;

    /* Background colors - glassmorphism enhanced dark theme */
    --body-bg: linear-gradient(135deg, #0D1117 0%, #161b22 50%, #21262d 100%);
    --card-bg: rgba(255, 255, 255, 0.08);
    --sidebar-bg: rgba(255, 255, 255, 0.12);
    --header-bg: rgba(255, 255, 255, 0.12);

    /* Text colors with enhanced contrast from nicestyle.css */
    --text-primary: #e6eefA;
    --text-secondary: #c3d0e5;
    --text-muted: #9ba3b4;

    /* Add accent text colors for emphasis */
    --text-accent: #4daafc;
    --text-success: #0ECFD3;
    --text-danger: #FF5A69;

    /* Border colors - glassmorphism enhanced */
    --border-color: rgba(255, 255, 255, 0.18);
    --border-light: rgba(255, 255, 255, 0.12);

    /* Glassmorphism specific variables for dark theme */
    --glass-bg: rgba(255, 255, 255, 0.08);
    --glass-bg-hover: rgba(255, 255, 255, 0.12);
    --glass-border: rgba(255, 255, 255, 0.18);
    --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.5);
    --glass-blur: blur(12px);
    --glass-border-radius: 10px;

    /* Notification colors - enhanced visibility */
    --notification-info-bg: rgba(77, 170, 252, 0.2);
    --notification-success-bg: rgba(14, 207, 211, 0.2);
    --notification-warning-bg: rgba(255, 185, 48, 0.2);
    --notification-danger-bg: rgba(255, 90, 105, 0.2);

    /* Enhanced gradient for dark theme with glassmorphism backdrop */
    --main-gradient: linear-gradient(135deg, rgba(0, 120, 212, 0.18) 0%, rgba(14, 207, 211, 0.12) 50%, rgba(255, 255, 255, 0.02) 100%);
    --backdrop-pattern: radial-gradient(circle at 20% 50%, rgba(0, 120, 212, 0.15) 0%, transparent 50%),
                       radial-gradient(circle at 80% 20%, rgba(14, 207, 211, 0.12) 0%, transparent 50%),
                       radial-gradient(circle at 40% 80%, rgba(77, 170, 252, 0.08) 0%, transparent 50%);
}

/* Dark theme glassmorphism specific adjustments */
[data-bs-theme="dark"] .card-header {
    background: rgba(255, 255, 255, 0.08) !important;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

[data-bs-theme="dark"] .card-footer {
    background: rgba(255, 255, 255, 0.04) !important;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

    /* Additional nicestyle.css inspired variables */
    --foreground: #9ba3b4;
    --foreground-intense: #e6eefA;
    --header-foreground: #c3d0e5;
    --accent-primary: #0078d4;
    --accent-secondary: #005ba4;
    --link-foreground: #4daafc;
    --link-foreground-hover: #8fc3fe;
    --button-primary-background: #0078d4;
    --button-primary-foreground: #ffffff;
    --button-primary-hover: #005ba4;
    --button-secondary-background: #25292E;
    --button-secondary-foreground: #c3d0e5;
    --button-secondary-border: rgba(255,255,255,0.3);
    --button-secondary-hover: #333940;
    --icon-button-hover-background: rgba(255,255,255,0.10);
    --search-input-background: transparent;
    --search-input-border: rgba(255,255,255,0.15);
    --search-input-border-active: #0078d4;
    --search-input-foreground: #9ba3b4;
    --search-input-placeholder-foreground: #9ba3b4;
    --card-hover-background: rgba(255,255,255,0.08);
    --card-border: rgba(255,255,255,0.2);
    --card-border-active: rgba(255,255,255,0.3);
    --feature-card-icon-background: rgba(255,255,255,0.07);
    --code-background: rgba(255,255,255,0.15);
    --code-foreground: #cccccc;
}

/* TEMP FIX: Disable all card/container background animations and shimmers */
.card,
.main-content,
.main-wrapper,
[data-bs-theme] .card,
[data-bs-theme] .main-content,
[data-bs-theme] .main-wrapper {
    animation: none !important;
    background-image: none !important;
    background: var(--card-bg, #fff) !important;
}

/* Theme-specific component styles */

/* Styling for unnested group items to behave like cards - Glassmorphism */
.groups-list .group-card>.row {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--glass-border-radius);
    box-shadow: var(--glass-shadow);
    /* p-3, shadow-sm, h-100 classes are applied in HTML */
}

/* Styling for unnested organisation items to behave like cards - Glassmorphism */
.orgs-list .org-card>.row {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--glass-border-radius);
    box-shadow: var(--glass-shadow);
    /* p-3, shadow-sm, h-100 classes are applied in HTML */
}

/* Ensure device-card wrapper has no styling to prevent double-layered effect */
.devices-list .device-card {
    background: none !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* Styling for unnested device items to behave like cards - Glassmorphism */
.devices-list .device-card>.row {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--glass-border-radius);
    box-shadow: var(--glass-shadow);
    /* p-3, shadow-sm, h-100 classes are applied in HTML */
}

/* Cards - Glassmorphism Enhanced */
[data-bs-theme="dark"] .card,
[data-bs-theme="dark"] .groups-list .group-card>.row,
[data-bs-theme="dark"] .orgs-list .org-card>.row,
[data-bs-theme="dark"] .devices-list .device-card>.row {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--glass-border-radius);
    box-shadow: var(--glass-shadow);
}

/* Tables */
[data-bs-theme="dark"] .table {
    color: var(--text-primary);
}

[data-bs-theme="dark"] .table thead th {
    color: var(--text-secondary);
}

[data-bs-theme="dark"] .table tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Forms - Glassmorphism Enhanced */
.form-control {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    color: var(--text-primary);
}

.form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

[data-bs-theme="dark"] .form-control {
    background: rgba(255, 255, 255, 0.08);
    border-color: var(--glass-border);
    color: var(--text-primary);
}

[data-bs-theme="dark"] .form-control:focus {
    background: rgba(255, 255, 255, 0.12);
    border-color: var(--primary);
}

/* Main content area theming - Enhanced for glassmorphism */
[data-bs-theme="light"] .main-wrapper {
    background: var(--backdrop-pattern), var(--main-gradient), var(--body-bg);
    background-attachment: fixed;
    background-size: 100% 100%, 100% 100%, cover;
}

[data-bs-theme="light"] .main-content {
    background-color: transparent;
}

[data-bs-theme="dark"] .main-wrapper {
    background: var(--backdrop-pattern), var(--main-gradient), var(--body-bg);
    background-attachment: fixed;
    background-size: 100% 100%, 100% 100%, cover;
}

[data-bs-theme="dark"] .main-content {
    background-color: transparent;
}

/* Removed blue and monochrome theme styles */

/* Theme Switcher Styling */
.theme-switcher {
    display: flex;
    align-items: center;
    gap: 8px;
}

.theme-option {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    cursor: pointer;
    position: relative;
    box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.1);
    transition: var(--transition-base);
}

.theme-option:hover {
    transform: scale(1.1);
}

.theme-option.active::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 10px;
    height: 10px;
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.theme-light {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

.theme-dark {
    background-color: #121212;
}

/* Theme Switcher UI Components */
.theme-switcher {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Theme Toggle Button */
.theme-toggle-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-base);
    background-color: transparent;
    border: none;
    color: var(--text-primary);
    position: relative;
    overflow: hidden;
}

.theme-toggle-btn:hover {
    background-color: rgba(var(--primary-rgb), 0.1);
    transform: scale(1.05);
}

.theme-toggle-btn i {
    font-size: 1.2rem;
    transition: var(--transition-base);
}

/* Light theme specific styling */
[data-bs-theme="light"] .theme-toggle-btn .fa-sun {
    opacity: 1;
    transform: rotate(0deg);
}

[data-bs-theme="light"] .theme-toggle-btn .fa-moon {
    opacity: 0;
    transform: rotate(90deg) scale(0.5);
    position: absolute;
}

/* Dark theme specific styling */
[data-bs-theme="dark"] .theme-toggle-btn .fa-moon {
    opacity: 1;
    transform: rotate(0deg);
}

[data-bs-theme="dark"] .theme-toggle-btn .fa-sun {
    opacity: 0;
    transform: rotate(-90deg) scale(0.5);
    position: absolute;
}

/* Ensure initials in avatars are always visible */
.avatar .initials {
    color: var(--text-primary) !important;
}

/* Improve avatar background for dark theme */
[data-bs-theme="dark"] .avatar {
    background-color: #23272f !important;
    border-color: var(--border-color) !important;
}



/* Add these new dashboard stat styling rules */
.dashboard-stat .card-title,
.dashboard-stat .mb-0,
.dashboard-stat i {
    color: #ffffff !important;
}

.dashboard-stat-alt .card-title,
.dashboard-stat-alt .mb-0,
.dashboard-stat-alt i {
    color: var(--text-primary) !important;
}

/* Add this to app/static/css/themes.css */

/* Add these new dashboard stat styling rules */
.dashboard-stat .card-title,
.dashboard-stat .mb-0,
.dashboard-stat i {
    color: #ffffff !important;
}

.dashboard-stat-alt .card-title,
.dashboard-stat-alt .mb-0,
.dashboard-stat-alt i {
    color: var(--text-primary) !important;
}

/* New rule for stat-card to make it theme-aware */
.stat-card .card-title,
.stat-card .mb-0,
.stat-card i {
    color: #ffffff !important;
}

/* Theme-specific overrides for light theme */
[data-bs-theme="light"] .stat-card:not(.bg-secondary) .card-title,
[data-bs-theme="light"] .stat-card:not(.bg-secondary) .mb-0,
[data-bs-theme="light"] .stat-card:not(.bg-secondary) i,
[data-bs-theme="light"] .dashboard-stat:not(.bg-secondary) .card-title,
[data-bs-theme="light"] .dashboard-stat:not(.bg-secondary) .mb-0,
[data-bs-theme="light"] .dashboard-stat:not(.bg-secondary) i {
    color: var(--text-primary) !important;
}

/* Special case for primary background cards in light theme */
[data-bs-theme="light"] .bg-primary .card-title,
[data-bs-theme="light"] .bg-primary .mb-0,
[data-bs-theme="light"] .bg-primary i,
[data-bs-theme="light"] .bg-primary-light .card-title,
[data-bs-theme="light"] .bg-primary-light .mb-0,
[data-bs-theme="light"] .bg-primary-light i,
[data-bs-theme="light"] .bg-primary-dark .card-title,
[data-bs-theme="light"] .bg-primary-dark .mb-0,
[data-bs-theme="light"] .bg-primary-dark i {
    color: var(--text-primary) !important;
}

/* Chat components theme-specific styling */
/* Light theme syntax highlighting */
[data-bs-theme="light"] .hljs {
    background: var(--body-bg);
    color: var(--text-primary);
}

/* Dark theme syntax highlighting */
[data-bs-theme="dark"] .hljs {
    background: #1a1a1a;
    color: #f8f9fa;
}

[data-bs-theme="dark"] .hljs-keyword,
[data-bs-theme="dark"] .hljs-selector-tag,
[data-bs-theme="dark"] .hljs-title {
    color: #569cd6;
}

[data-bs-theme="dark"] .hljs-string,
[data-bs-theme="dark"] .hljs-regexp {
    color: #ce9178;
}

/* Removed monochrome and blue theme syntax highlighting */

/* Additional chat component theming */
/* Chat message styling for dark theme */
[data-bs-theme="dark"] .chat-message.user {
    background: var(--body-bg);
    border-color: #2d2d2d;
}

[data-bs-theme="dark"] .chat-message.ai {
    background: var(--card-bg);
    border-color: #242424;
}

[data-bs-theme="dark"] #chatOffcanvas .offcanvas-header {
    border-bottom: 1px solid var(--border-color);
}

[data-bs-theme="dark"] .offcanvas-body {
    background-color: var(--body-bg);
}

/* Removed monochrome and blue theme chat styling */

/* Wegcoin badge styling for different themes */
[data-bs-theme="dark"] .wegcoin-badge {
    background-color: rgba(79, 123, 255, 0.1);
    color: var(--primary);
}

/* Removed monochrome and blue theme wegcoin badge styling */

/* Thought process styling for different themes */
[data-bs-theme="dark"] .thought-process {
    background: rgba(255, 255, 255, 0.03);
}

/* Theme-specific styling for thought process */

/* Theme-specific progress bar styling */
[data-bs-theme="light"] .health-score .progress {
    background-color: rgba(0, 0, 0, 0.08);
}

[data-bs-theme="dark"] .health-score .progress {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Additional gauge theming for health-gauge-container */
[data-bs-theme="dark"] .health-gauge-container {
    filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.3));
}

/* Gauge styling for dark theme */

/* Canvas theming handled in JavaScript, these are just fallbacks */
[data-bs-theme="dark"] canvas {
    filter: brightness(1.05) contrast(1.05);
}

/* Chart theme adaptations */
[data-bs-theme="dark"] .u-title {
    color: var(--text-primary) !important;
}

/* Enhanced dark theme styling with nicestyle.css contrast improvements */
[data-bs-theme="dark"] {
    /* Button styling improvements */
    .btn-primary {
        background-color: var(--button-primary-background);
        border-color: var(--button-primary-background);
        color: var(--button-primary-foreground);
    }

    .btn-primary:hover,
    .btn-primary:focus {
        background-color: var(--button-primary-hover);
        border-color: var(--button-primary-hover);
    }

    .btn-secondary {
        background-color: var(--button-secondary-background);
        border-color: var(--button-secondary-border);
        color: var(--button-secondary-foreground);
    }

    .btn-secondary:hover,
    .btn-secondary:focus {
        background-color: var(--button-secondary-hover);
    }

    /* Enhanced card styling with better contrast */
    .card {
        background-color: var(--card-bg);
        border: 1px solid var(--card-border);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }

    .card:hover {
        background-color: var(--card-hover-background);
        border-color: var(--card-border-active);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
    }

    /* Specific card improvements for better visibility */
    .card-body {
        background-color: var(--card-bg);
        color: var(--text-primary);
    }

    .card-header {
        background-color: rgba(255, 255, 255, 0.08);
        border-bottom: 1px solid var(--card-border);
        color: var(--header-foreground);
    }

    .card-footer {
        background-color: rgba(255, 255, 255, 0.06);
        border-top: 1px solid var(--card-border);
        color: var(--text-secondary);
    }

    /* Link styling improvements */
    a {
        color: var(--link-foreground);
    }

    a:hover,
    a:focus {
        color: var(--link-foreground-hover);
    }

    /* Form input styling */
    .form-control,
    .form-select {
        background-color: var(--search-input-background);
        border-color: var(--search-input-border);
        color: var(--search-input-foreground);
    }

    .form-control:focus,
    .form-select:focus {
        border-color: var(--search-input-border-active);
        box-shadow: 0 0 0 0.2rem rgba(0, 120, 212, 0.25);
    }

    .form-control::placeholder {
        color: var(--search-input-placeholder-foreground);
    }

    /* Code block styling */
    code,
    pre {
        background-color: var(--code-background);
        color: var(--code-foreground);
    }

    /* Icon button hover effects */
    .btn-icon:hover,
    .icon-button:hover {
        background-color: var(--icon-button-hover-background);
    }

    /* Device card specific improvements */
    .device-card,
    .stat-card {
        background-color: var(--card-bg);
        border: 1px solid var(--card-border);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        transition: all 0.2s ease;
    }

    .device-card:hover,
    .stat-card:hover {
        background-color: var(--card-hover-background);
        border-color: var(--card-border-active);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
        transform: translateY(-1px);
    }

    /* Progress bars and badges in dark theme */
    .progress {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .badge {
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    /* Table styling improvements */
    .table {
        color: var(--text-primary);
    }

    .table th {
        border-bottom: 1px solid var(--border-color);
        color: var(--header-foreground);
    }

    .table td {
        border-bottom: 1px solid var(--border-light);
    }

    .table-striped > tbody > tr:nth-of-type(odd) > td {
        background-color: rgba(255, 255, 255, 0.02);
    }
}

[data-bs-theme="dark"] .u-legend .u-label {
    color: var(--text-secondary) !important;
}

[data-bs-theme="dark"] .u-axis .u-label {
    color: var(--text-secondary) !important;
    fill: var(--text-secondary) !important;
}

[data-bs-theme="dark"] .u-axis .u-tick text {
    color: var(--text-muted) !important;
    fill: var(--text-muted) !important;
}

/* Fix chart backgrounds */
[data-bs-theme="dark"] .uplot {
    background-color: var(--card-bg) !important;
}

/* Chart tooltips for dark themes */
[data-bs-theme="dark"] .u-tooltip {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3) !important;
}

/* Override Bootstrap's text-muted to use our theme-specific colors */
[data-bs-theme="light"] .text-muted {
    color: #6B7280 !important;
    /* Match our light theme text-muted */
}

[data-bs-theme="dark"] .text-muted {
    color: #A0AEC0 !important;
    /* Match our dark theme text-muted */
}

/* Only light and dark themes are supported */

/* Enhanced Search Input Styling - Glassmorphism */
.search-box .input-group {
    border-radius: var(--glass-border-radius);
    overflow: hidden;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

/* Make the borders match between input-group-text and form-control */
.search-box .input-group-text,
.search-box .form-control {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border-color: var(--glass-border);
    transition: var(--transition-base);
}

/* Focus state for the entire search box */
.search-box .form-control:focus {
    box-shadow: none;
    border-color: var(--primary);
}

/* Make input-group-text border match form-control on focus */
.search-box .form-control:focus~.input-group-text,
.search-box .form-control:focus+.input-group-text,
.search-box .input-group-text+.form-control:focus {
    border-color: var(--primary);
}

/* Light theme search box glassmorphism */
[data-bs-theme="light"] .search-box .form-control {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border-color: var(--glass-border);
    color: var(--text-primary);
}

[data-bs-theme="light"] .search-box .input-group-text {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border-color: var(--glass-border);
    color: var(--text-secondary);
}

/* Ensure text is legible in dark themes */
[data-bs-theme="dark"] .search-box .form-control {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border-color: var(--glass-border);
}

/* Make borders more visible in dark themes */
[data-bs-theme="dark"] .search-box .input-group-text {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border-color: var(--glass-border);
    color: var(--text-secondary);
}

/* Consistent focus state styling for all themes */
[data-bs-theme="dark"] .search-box .form-control:focus,
[data-bs-theme="dark"] .search-box .form-control:focus~.input-group-text,
[data-bs-theme="dark"] .search-box .form-control:focus+.input-group-text,
[data-bs-theme="dark"] .search-box .input-group-text+.form-control:focus {
    border-color: var(--primary);
}

/* Focus styles for light and dark themes */

/* Placeholder text styling for better legibility */
[data-bs-theme="dark"] .search-box .form-control::placeholder {
    color: var(--text-muted);
    opacity: 0.7;
}

/* Override search icon color to match theme */
[data-bs-theme="dark"] .search-box .input-group-text i {
    color: var(--text-secondary);
}