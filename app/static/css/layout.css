/* Modern Layout Components for Wegweiser
   Provides structural styling for the application
*/

/* App Container */
.app-container {
  display: flex;
  min-height: 100vh;
}

/* Main Wrapper */
.main-wrapper {
  flex: 1;
  margin-left: 260px;
  padding-top: 60px;
  /* Changed from 70px to match sidebar header */
  min-height: 100vh;
  transition: var(--transition-base);
  overflow-x: hidden;
  background-color: var(--body-bg, #F8F9FA);
  /* Default fallback */
}

/* Main Content */
.main-content {
  padding: var(--spacer-xl);
  background-color: transparent;
  width: 100%;
}

/* Header - Glassmorphism Enhanced */
.top-header {
  position: fixed;
  top: 0;
  right: 0;
  left: 260px;
  height: 60px;
  /* Changed from 70px to match sidebar header */
  background: var(--header-bg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  box-shadow: var(--glass-shadow);
  z-index: 100;
  transition: var(--transition-base);
  border-bottom: 1px solid var(--glass-border);
}

.top-header .navbar {
  height: 100%;
  padding: 0 var(--spacer-lg);
}

.top-header .navbar-brand {
  font-weight: 600;
  color: var(--text-primary);
}

.top-header .nav-link {
  color: var(--text-primary);
  padding: 0.5rem;
  border-radius: var(--border-radius);
  transition: var(--transition-base);
}

.top-header .nav-link:hover {
  background-color: rgba(171, 168, 69, 0.1);
  color: var(--primary);
}

.top-header .nav-right-links {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.top-header .dropdown-toggle::after {
  display: none;
}

.top-header .user-profile {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(171, 168, 69, 0.3);
}

.top-header .badge-notify {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 0.65rem;
  padding: 0.2rem 0.4rem;
  border-radius: 50%;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--accent-gold);
  color: white;
}

.btn-toggle {
  display: none;
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.25rem;
  margin-right: 0.5rem;
  border-radius: var(--border-radius);
  transition: var(--transition-base);
}

.btn-toggle:hover {
  background-color: rgba(171, 168, 69, 0.1);
  color: var(--primary);
}

/* Sidebar - Glassmorphism Enhanced */
.sidebar-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 260px;
  height: 100vh;
  background: var(--sidebar-bg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border-right: 1px solid var(--glass-border);
  z-index: 1030;
  transition: var(--transition-base);
  box-shadow: var(--glass-shadow);
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  height: 60px;
  /* Already correct */
  display: flex;
  align-items: center;
  padding: 0 var(--spacer);
  border-bottom: 1px solid var(--accent-gold);
  background-color: var(--accent-gold);
  color: white;
  /* Added explicit background color */
  flex-shrink: 0;
}

.sidebar-header .logo-img {
  width: 30px;
  /* Reduced from 36px */
  height: 30px;
  margin-right: 8px;
}

.sidebar-header h5 {
  margin: 0;
  font-weight: 600;
  font-size: 1.1rem;
  /* Reduced from default heading size */
  color: white;
}

.sidebar-header .sidebar-close {
  display: none;
  margin-left: auto;
  background: none;
  border: none;
  color: white;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.25rem;
}

.sidebar-nav {
  flex-grow: 1;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(171, 168, 69, 0.4) transparent;
  padding: 0;
  /* Remove padding, we'll add it to sections */
}

.sidebar-nav::-webkit-scrollbar {
  width: 3px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background-color: rgba(171, 168, 69, 0.4);
  border-radius: 3px;
}

.menu-label {
  font-size: 0.7rem;
  /* Reduced from 0.75rem */
  font-weight: 600;
  text-transform: uppercase;
  color: var(--accent-gold);
  padding: 0.5rem var(--spacer);
  /* Reduced top/bottom padding */
  letter-spacing: 0.5px;
  margin-top: 0.5rem;
  margin-bottom: 0.25rem;
  /* Add bottom margin */
}

.sidebar-nav ul {
  padding: 0;
  margin: 0 0 0.5rem 0;
  /* Add bottom margin for sections */
  list-style: none;
}

.sidebar-nav .nav-link {
  display: flex;
  align-items: center;
  color: var(--text-secondary);
  padding: 0.4rem var(--spacer);
  /* Reduced from 0.5rem */
  margin: 1px var(--spacer-sm);
  /* Reduced from 2px */
  border-radius: var(--border-radius);
  transition: var(--transition-base);
  font-size: 0.9rem;
  /* Reduced font size */
}

.sidebar-nav .nav-link .parent-icon {
  width: 20px;
  /* Reduced from 24px */
  text-align: center;
  margin-right: var(--spacer-sm);
  font-size: 0.9rem;
  /* Smaller icons */
  color: var(--accent-gold);
}

.sidebar-nav .nav-link .menu-title {
  flex: 1;
}

.sidebar-nav .nav-link:hover {
  background-color: rgba(171, 168, 69, 0.1);
  color: var(--primary);
}

.sidebar-nav .nav-link.active {
  background-color: rgba(171, 168, 69, 0.15);
  color: var(--primary);
  font-weight: 500;
}

/* Sidebar submenu */
.sidebar-nav .submenu {
  padding-left: var(--spacer);
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
}

.sidebar-nav .submenu.show {
  max-height: 1000px;
}

.sidebar-nav .has-arrow {
  position: relative;
}

.sidebar-nav .has-arrow::after {
  content: '\f105';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  position: absolute;
  right: var(--spacer);
  transition: transform 0.3s ease;
  color: var(--accent-gold);
}

.sidebar-nav .has-arrow[aria-expanded="true"]::after {
  transform: rotate(90deg);
}

/* Improve section grouping with subtle borders */
.sidebar-section {
  margin-bottom: 0.5rem;
  border-bottom: 1px solid rgba(171, 168, 69, 0.2);
  padding-bottom: 0.5rem;
}

.sidebar-section:last-child {
  border-bottom: none;
}

/* Collapsible sections for less used areas */
.collapsible-section {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
}

.collapsible-section.expanded {
  max-height: 1000px;
}

.section-toggle {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-toggle::after {
  content: '\f078';
  /* fa-chevron-down */
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  font-size: 0.7rem;
  transition: transform 0.3s ease;
  color: var(--accent-gold);
}

.section-toggle.expanded::after {
  transform: rotate(180deg);
}

/* Administrative section - less used, so collapsible */
.admin-section .collapsible-section {
  padding-left: 0.5rem;
}

/* Footer Visibility Fix */
.main-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100%;
}

.main-content {
  flex: 1 0 auto;
}

.page-footer {
  flex-shrink: 0;
  margin-top: auto;
  position: relative;
  z-index: 10;
  /* Ensure footer appears above other elements */
  background: inherit;
  /* Match the background of parent for consistency */
  padding: 1rem 0;
  border-top: 1px solid rgba(171, 168, 69, 0.3);
  /* subtle separator with gold tone */
}

/* Fix for sticky footer on mobile */
@media (max-width: 768px) {
  .main-wrapper {
    min-height: calc(100vh - 60px);
    /* Adjust based on your header height */
  }
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .btn-toggle {
    display: block;
  }

  .sidebar-wrapper {
    transform: translateX(-100%);
  }

  .sidebar-wrapper.show {
    transform: translateX(0);
  }

  .sidebar-header .sidebar-close {
    display: block;
  }

  .main-wrapper,
  .top-header,
  .page-footer {
    margin-left: 0;
  }

  /* Overlay for mobile sidebar - ensure it's hidden by default and transparent */
  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0);
    /* Transparent background */
    z-index: 1020;
    /* Ensure it's below sidebar but above content if shown */
    display: none;
    /* Hidden by default */
    transition: background-color 0.3s ease-in-out;
    /* Smooth transition if shown */
  }

  .overlay.show {
    display: block;
    background-color: rgba(0, 0, 0, 0.5);
    /* Semi-transparent black when shown */
  }
}