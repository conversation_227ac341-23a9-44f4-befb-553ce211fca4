/* Glassmorphism Utilities for Wegweiser
   Modern frosted glass effects with theme compatibility
*/

/* Core Glassmorphism Classes */
.glass {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border-radius: var(--glass-border-radius);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.glass-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-hover:hover {
    background: var(--glass-bg-hover);
    transform: translateY(-1px);
    box-shadow: var(--glass-shadow), 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Glassmorphism Variants */
.glass-subtle {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.12);
}

.glass-medium {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
}

.glass-strong {
    background: rgba(255, 255, 255, 0.35);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.25);
}

/* Dark theme adjustments */
[data-bs-theme="dark"] .glass-subtle {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.08);
}

[data-bs-theme="dark"] .glass-strong {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Glassmorphism Button Styles */
.btn-glass {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--glass-border-radius);
    color: var(--text-primary);
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-glass:hover {
    background: var(--glass-bg-hover);
    transform: translateY(-2px);
    box-shadow: var(--glass-shadow), 0 6px 20px rgba(0, 0, 0, 0.15);
    color: var(--text-primary);
}

.btn-glass:active {
    transform: translateY(0);
    box-shadow: var(--glass-shadow);
}

/* Glassmorphism Card Styles */
.card-glass {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--glass-border-radius);
    box-shadow: var(--glass-shadow);
}

.card-glass .card-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border-bottom: 1px solid var(--glass-border);
}

.card-glass .card-footer {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border-top: 1px solid var(--glass-border);
}

/* Dark theme card adjustments */
[data-bs-theme="dark"] .card-glass .card-header {
    background: rgba(255, 255, 255, 0.08);
}

[data-bs-theme="dark"] .card-glass .card-footer {
    background: rgba(255, 255, 255, 0.04);
}

/* Glassmorphism Navigation */
.nav-glass {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
}

.nav-glass .nav-link {
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.nav-glass .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border-radius: 8px;
}

/* Glassmorphism Modal Backdrop Enhancement */
.modal-backdrop-glass {
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

/* Glassmorphism Input Fields */
.form-control-glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    color: var(--text-primary);
}

.form-control-glass:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

.form-control-glass::placeholder {
    color: var(--text-muted);
    opacity: 0.8;
}

/* Glassmorphism Dropdown */
.dropdown-menu-glass {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--glass-border-radius);
    box-shadow: var(--glass-shadow);
}

.dropdown-menu-glass .dropdown-item {
    color: var(--text-primary);
    transition: all 0.2s ease;
}

.dropdown-menu-glass .dropdown-item:hover {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

/* Glassmorphism Badge */
.badge-glass {
    background: var(--glass-bg);
    backdrop-filter: blur(6px);
    -webkit-backdrop-filter: blur(6px);
    border: 1px solid var(--glass-border);
    color: var(--text-primary);
}

/* Glassmorphism Alert/Notification */
.alert-glass {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--glass-border-radius);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .glass,
    .card-glass,
    .nav-glass {
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
    }
}

/* Accessibility: Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    .glass-hover,
    .btn-glass,
    .nav-glass .nav-link {
        transition: none;
    }
    
    .glass-hover:hover,
    .btn-glass:hover {
        transform: none;
    }
}

/* High contrast mode adjustments */
@media (prefers-contrast: high) {
    .glass,
    .card-glass,
    .btn-glass {
        border-width: 2px;
        backdrop-filter: none;
        -webkit-backdrop-filter: none;
        background: var(--card-bg);
    }
}
