/* File: static/css/base.css */

:root {
    /* Fonts */
    --font-family-base: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    --font-family-headings: 'Manrope', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;

    /* Brand Colors */
    --primary: #2851E7;
    --primary-rgb: 40, 81, 231;
    --primary-dark: #1A40D5;
    --secondary: #5B6B86;
    --success: #06A6A9;
    --danger: #E83C4B;
    --warning: #FFAB00;
    --info: #39D0D8;

    /* Bootstrap modal z-index overrides */
    --bs-modal-zindex: 1050;
    --bs-modal-backdrop-zindex: 1040;

    /* Background colors */
    --body-bg: #F8F9FA;
    --card-bg: #FFFFFF;
    --sidebar-bg: #FFFFFF;
    --header-bg: #FFFFFF;

    /* Text colors */
    --text-primary: #1A1D21;
    --text-secondary: #5B6B86;

    /* Border colors */
    --border-color: #E2E8F0;
    --border-light: #F0F4F8;

    /* Spacing - Enhanced for better whitespace */
    --spacer: 1rem;
    --spacer-xs: 0.25rem;
    --spacer-sm: 0.5rem;
    --spacer-md: 1rem;
    --spacer-lg: 1.5rem;
    --spacer-xl: 2.5rem;
    --spacer-xxl: 4rem;

    /* Border radius - Updated for modern look */
    --border-radius-xs: 0.25rem;
    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1.25rem;

    /* Transitions - Enhanced for smoother effects */
    --transition-fast: all 0.15s ease-in-out;
    --transition-base: all 0.25s ease-in-out;
    --transition-slow: all 0.4s ease-in-out;

    /* Shadows - Enhanced for more depth */
    --shadow-xs: 0 1px 3px rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 2px 6px rgba(0, 0, 0, 0.07);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 12px 32px rgba(0, 0, 0, 0.12);

    /* Typography */
    --font-sans: 'Inter', 'SF Pro Text', 'Noto Sans', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-heading: 'Manrope', var(--font-sans);
    --font-mono: 'SF Mono', 'Roboto Mono', Menlo, monospace;

    /* Font size hierarchy */
    --font-size-xs: 0.75rem;
    /* 12px */
    --font-size-sm: 0.875rem;
    /* 14px */
    --font-size-base: 1rem;
    /* 16px */
    --font-size-md: 1.125rem;
    /* 18px */
    --font-size-lg: 1.25rem;
    /* 20px */
    --font-size-xl: 1.5rem;
    /* 24px */
    --font-size-2xl: 1.875rem;
    /* 30px */
    --font-size-3xl: 2.25rem;
    /* 36px */

    /* Adjust line heights for better readability */
    --line-height-tight: 1.2;
    --line-height-base: 1.5;
    --line-height-relaxed: 1.75;
    --line-height-loose: 2;

    /* Font weights */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
}

/* General Styles - Glassmorphism Enhanced */
body {
    font-family: var(--font-family-base);
    background: var(--body-bg);
    color: var(--text-primary);
    margin: 0;
    min-height: 100vh;
    transition: var(--transition-base);
    line-height: var(--line-height-base);
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 0.95rem;
    letter-spacing: -0.01em;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: var(--font-family-headings);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    color: var(--text-primary);
    margin-bottom: var(--spacer);
    letter-spacing: -0.02em;
}

h1,
.h1 {
    font-family: var(--font-heading);
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    letter-spacing: -0.02em;
    margin-bottom: 1rem;
}

h2,
.h2 {
    font-family: var(--font-heading);
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    letter-spacing: -0.01em;
    margin-bottom: 0.75rem;
}

h3,
.h3 {
    font-family: var(--font-heading);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    margin-bottom: 0.75rem;
}

h4,
.h4 {
    font-family: var(--font-heading);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-base);
    margin-bottom: 0.5rem;
}

h5,
.h5 {
    font-family: var(--font-heading);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-base);
    margin-bottom: 0.5rem;
}

h6,
.h6 {
    font-family: var(--font-heading);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-base);
    margin-bottom: 0.5rem;
}

p {
    margin-bottom: var(--spacer);
}

a {
    color: var(--primary);
    text-decoration: none;
    transition: var(--transition-base);
    position: relative;
}

a:hover {
    color: var(--primary-dark);
    text-decoration: none;
}

/* Typography utility classes */
.text-xs {
    font-size: var(--font-size-xs);
}

.text-sm {
    font-size: var(--font-size-sm);
}

.text-base {
    font-size: var(--font-size-base);
}

.text-md {
    font-size: var(--font-size-md);
}

.text-lg {
    font-size: var(--font-size-lg);
}

.text-xl {
    font-size: var(--font-size-xl);
}

.text-2xl {
    font-size: var(--font-size-2xl);
}

.text-3xl {
    font-size: var(--font-size-3xl);
}

/* Font weight utilities */
.font-light {
    font-weight: var(--font-weight-light);
}

.font-normal {
    font-weight: var(--font-weight-normal);
}

.font-medium {
    font-weight: var(--font-weight-medium);
}

.font-semibold {
    font-weight: var(--font-weight-semibold);
}

.font-bold {
    font-weight: var(--font-weight-bold);
}

/* Line height utilities */
.leading-tight {
    line-height: var(--line-height-tight);
}

.leading-base {
    line-height: var(--line-height-base);
}

.leading-relaxed {
    line-height: var(--line-height-relaxed);
}

.leading-loose {
    line-height: var(--line-height-loose);
}

/* Letter spacing utilities */
.tracking-tight {
    letter-spacing: -0.025em;
}

.tracking-normal {
    letter-spacing: 0;
}

.tracking-wide {
    letter-spacing: 0.025em;
}

.tracking-wider {
    letter-spacing: 0.05em;
}

/* Layout Components */
.app-container {
    display: flex;
    min-height: 100vh;
}

/* Main Content Area */
.main-wrapper {
    flex: 1;
    margin-left: 260px;
    padding-top: 70px;
    min-height: 100vh;
    transition: var(--transition-base);
}

.main-content {
    padding: var(--spacer-xl);
    max-width: 1600px;
    margin: 0 auto;
}

/* Card Enhancements - Glassmorphism Style */
.card {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--glass-border-radius);
    box-shadow: var(--glass-shadow);
    transition: var(--transition-base);
    margin-bottom: var(--spacer-lg);
    overflow: hidden;
}

/* Removed hover effects per user preference */
.card-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border-bottom: 1px solid var(--glass-border);
    padding: var(--spacer-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-body {
    padding: var(--spacer-lg);
    background: transparent;
}

.card-footer {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border-top: 1px solid var(--glass-border);
    padding: var(--spacer-lg);
}

/* Dashboard Stat Cards - Glassmorphism Style */
.stat-card {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--glass-border-radius);
    box-shadow: var(--glass-shadow);
    transition: var(--transition-base);
}

/* Removed hover effects per user preference */

/* Theme-compatible backgrounds for dashboard cards */
.bg-primary-light {
    background-color: rgba(var(--primary-rgb), 0.8) !important;
}

.bg-primary-dark {
    background-color: var(--primary-dark) !important;
}

/* Ensure text is visible on all backgrounds */
.bg-primary,
.bg-primary-light,
.bg-primary-dark,
.bg-danger,
.bg-warning,
.bg-info {
    color: #fff !important;
}

.bg-primary i,
.bg-primary-light i,
.bg-primary-dark i,
.bg-danger i,
.bg-warning i,
.bg-info i,
.bg-primary .card-title,
.bg-primary-light .card-title,
.bg-primary-dark .card-title,
.bg-danger .card-title,
.bg-warning .card-title,
.bg-info .card-title,
.bg-primary .mb-0,
.bg-primary-light .mb-0,
.bg-primary-dark .mb-0,
.bg-danger .mb-0,
.bg-warning .mb-0,
.bg-info .mb-0 {
    color: #fff !important;
}

/* For cards that need to use current theme text color */
.bg-secondary {
    color: var(--text-primary) !important;
    background-color: rgba(var(--secondary-rgb, 91, 107, 134), 0.15) !important;
}

.bg-secondary i,
.bg-secondary .card-title,
.bg-secondary .mb-0 {
    color: var(--text-primary) !important;
}

/* For nested cards, ensure consistent height and styling */
.card .card {
    margin-bottom: 0;
    height: 100%;
    box-shadow: var(--shadow-sm);
}

/* Light theme specific card fixes */
[data-bs-theme="light"] .card.stat-card {
    box-shadow: var(--shadow-sm);
}

[data-bs-theme="light"] .bg-secondary {
    background-color: #f8f9fa !important;
}

/* Use standard card header styling from base.css */
.card-header {
    border-bottom: 1px solid var(--border-light);
    background-color: rgba(var(--primary-rgb), 0.05);
}

.card-header i {
    color: var(--primary);
}

/* Table Enhancements */
.table {
    margin-bottom: 0;
    width: 100%;
}

.table thead th {
    border-top: none;
    border-bottom: 2px solid var(--border-color);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    padding: var(--spacer) var(--spacer-lg);
    color: var(--text-secondary);
}

.table tbody td {
    padding: var(--spacer-lg);
    vertical-align: middle;
    border-top: none;
    border-bottom: 1px solid var(--border-light);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

.table tbody tr:hover {
    background-color: rgba(var(--primary-rgb), 0.04);
}

/* Badge Styling - Glassmorphism Enhanced */
.badge {
    font-weight: var(--font-weight-medium);
    padding: 0.35em 0.65em;
    border-radius: var(--border-radius-xs);
    text-transform: uppercase;
    letter-spacing: 0.03em;
    font-size: 0.75rem;
    backdrop-filter: blur(6px);
    -webkit-backdrop-filter: blur(6px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Button Enhancements - Glassmorphism Style */
.btn {
    font-weight: var(--font-weight-medium);
    padding: 0.5rem 1.25rem;
    border-radius: var(--glass-border-radius);
    transition: var(--transition-base);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

/* Ensure modal close buttons can be clicked */
.btn-close {
    z-index: 1070 !important;
    position: relative !important;
}

.btn-primary {
    background: rgba(var(--primary-rgb), 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(var(--primary-rgb), 0.3);
    color: white;
}

.btn-primary:hover {
    background: rgba(var(--primary-rgb), 0.9);
    border-color: var(--primary);
    transform: translateY(-1px);
    box-shadow: var(--glass-shadow), 0 4px 8px rgba(var(--primary-rgb), 0.25);
    color: white;
}

.btn-outline-primary {
    background: rgba(var(--primary-rgb), 0.1);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    color: var(--primary);
    border: 1px solid rgba(var(--primary-rgb), 0.3);
}

.btn-outline-primary:hover {
    background: rgba(var(--primary-rgb), 0.8);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--glass-shadow);
}

/* Modal styling - comprehensive fix for scrolling issues */
.modal {
    z-index: 1050 !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    overflow-x: hidden !important;
    overflow-y: auto !important;
    padding-right: 0 !important;
    /* Fix for modal shift */
}

.modal-backdrop {
    z-index: 1040 !important;
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
}

.modal-dialog {
    position: relative !important;
    z-index: 1060 !important;
    margin: 1.75rem auto !important;
    pointer-events: all !important;
    display: flex !important;
    flex-direction: column !important;
    max-width: 800px !important;
    /* Wider default for readability */
}

.modal-content {
    position: relative !important;
    z-index: 1060 !important;
    background-color: var(--card-bg) !important;
    box-shadow: var(--glass-shadow), 0 5px 15px rgba(0, 0, 0, 0.5) !important;
    pointer-events: all !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: var(--glass-border-radius) !important;
    overflow: visible !important;
    /* Critical fix */
    max-height: none !important;
    /* Remove height constraint */
    display: flex !important;
    flex-direction: column !important;
    width: 100% !important;
    /* Keep modals opaque per user preference */
    opacity: 1 !important;
}

.modal-body {
    position: relative !important;
    overflow-y: auto !important;
    /* Make body scrollable */
    max-height: calc(95vh - 120px) !important;
    /* Adjust max height accounting for header/footer */
    padding: 1rem !important;
}

.modal-header,
.modal-footer {
    flex-shrink: 0 !important;
    /* Prevent header/footer from shrinking */
}

.modal-header .btn-close {
    position: relative !important;
    z-index: 1070 !important;
    cursor: pointer !important;
    opacity: 1 !important;
    pointer-events: all !important;
}

/* Force modal visibility */
.modal.show {
    display: block !important;
    padding-right: 0 !important;
}

.modal.show .modal-dialog {
    transform: none !important;
    margin: 1.75rem auto !important;
}

/* Handle body class when modal is open */
body.modal-open {
    overflow: hidden;
    padding-right: 0 !important;
    /* Prevent layout shift */
}

/* Fix for mobile */
@media (max-width: 768px) {
    .modal-dialog {
        margin: 0.5rem auto !important;
    }

    .modal-body {
        max-height: calc(95vh - 100px) !important;
    }
}

/* Scrollbar styling - Refined for better visual integration */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
    margin: 4px 0;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    transition: var(--transition-base);
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.25);
}

/* Better dark theme scrollbars */
[data-bs-theme="dark"] ::-webkit-scrollbar-track {
    background: transparent;
}

[data-bs-theme="dark"] ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.15);
}

[data-bs-theme="dark"] ::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.25);
}

/* Enhanced Sidebar Styling - Glassmorphism */
.sidebar-wrapper {
    width: 260px;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 999;
    background: var(--sidebar-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border-right: 1px solid var(--glass-border);
    transition: var(--transition-base);
    display: flex;
    flex-direction: column;
    box-shadow: var(--glass-shadow);
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacer-lg);
    overflow: hidden;
    border-bottom: 1px solid var(--glass-border) !important;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

[data-bs-theme="dark"] .sidebar-header {
    border-bottom: 1px solid var(--glass-border) !important;
    background: rgba(255, 255, 255, 0.08) !important;
}

.sidebar-full-logo {
    max-width: 80%;
    height: auto;
    display: block;
    object-fit: contain;
    margin-right: 12px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.05));
    transition: var(--transition-base);
}

.sidebar-close {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    width: 32px;
    height: 32px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-base);
}

.sidebar-close:hover {
    background-color: rgba(var(--primary-rgb), 0.1);
    color: var(--primary);
}

.sidebar-nav {
    padding: var(--spacer);
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
}

.menu-label {
    padding: var(--spacer-sm) var(--spacer);
    font-size: 0.75rem;
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.08em;
    color: var(--text-secondary);
    margin-top: var(--spacer);
}

.sidebar-section {
    margin-bottom: var(--spacer);
}

.sidebar-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-nav ul li {
    margin-bottom: 2px;
}

.sidebar-nav .nav-link {
    display: flex;
    align-items: center;
    padding: var(--spacer-sm) var(--spacer);
    color: var(--text-primary);
    border-radius: var(--border-radius);
    transition: var(--transition-base);
}

.sidebar-nav .nav-link:hover {
    background-color: rgba(var(--primary-rgb), 0.08);
}

.sidebar-nav .nav-link.active {
    background-color: rgba(var(--primary-rgb), 0.12);
    color: var(--primary);
    font-weight: var(--font-weight-medium);
}

.sidebar-nav .parent-icon {
    margin-right: var(--spacer);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    font-size: 1rem;
}

.sidebar-nav .menu-title {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.section-toggle {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.section-toggle::after {
    content: "\f107";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    font-size: 0.875rem;
    transition: var(--transition-base);
}

.collapsible-section {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: max-height;
    transform: translateZ(0);
    /* Trigger GPU acceleration */
}

.collapsible-section.expanded {
    max-height: 2000px;
    /* Larger value to ensure it can fit any content */
}

/* Responsive: Mobile Sidebar */
@media (max-width: 992px) {
    .sidebar-wrapper {
        transform: translateX(-100%);
    }

    .sidebar-wrapper.active {
        transform: translateX(0);
    }

    .main-wrapper {
        margin-left: 0;
    }

    .sidebar-full-logo {
        max-width: 70%;
    }

    .overlay.active {
        display: block !important;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 998;
    }
}

/* Ensure the btn-close is clickable - this provides additional specificity */
.modal-header .close,
.modal-header .btn-close {
    padding: 1rem;
    margin: -1rem -1rem -1rem auto;
    cursor: pointer;
    z-index: 1070 !important;
    /* Match our new z-index scheme */
    position: relative !important;
}

/* Fix transparency issues - make modals and notifications completely opaque */

/* Bootstrap Modal Fixes */
.modal-content {
    background-color: #ffffff !important;
    opacity: 1 !important;
}

.modal-header {
    background-color: #ffffff !important;
    opacity: 1 !important;
}

.modal-body {
    background-color: #ffffff !important;
    opacity: 1 !important;
}

.modal-footer {
    background-color: #ffffff !important;
    opacity: 1 !important;
}

/* Notification Container Fixes */
#notification-container {
    opacity: 1 !important;
}

#notification-container .alert {
    background-color: #ffffff !important;
    opacity: 1 !important;
}

/* Ensure modal backdrop is properly opaque */
.modal-backdrop {
    background-color: rgba(0, 0, 0, 0.5) !important;
    opacity: 0.5 !important;
}

/* Legacy Lobibox fixes (if still used) */
.lobibox .lobibox-body {
    background-color: rgba(255, 255, 255, 1.0) !important;
}

.lobibox-notify {
    opacity: 1 !important;
}

.alert-success {
    background-color: rgba(6, 166, 169, 0.15) !important;
    border-color: var(--success) !important;
    color: var(--success) !important;
}

.alert-danger {
    background-color: rgba(232, 60, 75, 0.15) !important;
    border-color: var(--danger) !important;
    color: var(--danger) !important;
}

.alert-warning {
    background-color: rgba(255, 171, 0, 0.15) !important;
    border-color: var(--warning) !important;
    color: var(--warning) !important;
}

.alert-info {
    background-color: rgba(57, 208, 216, 0.15) !important;
    border-color: var(--info) !important;
    color: var(--info) !important;
}

/* Dark theme specific fixes for modals and notifications */
[data-bs-theme="dark"] .modal-content {
    background-color: #0D1117 !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: #e6eefA !important;
    opacity: 1 !important;
}

[data-bs-theme="dark"] .modal-header {
    background-color: #0D1117 !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: #e6eefA !important;
    opacity: 1 !important;
}

[data-bs-theme="dark"] .modal-body {
    background-color: #0D1117 !important;
    color: #e6eefA !important;
    opacity: 1 !important;
}

[data-bs-theme="dark"] .modal-footer {
    background-color: #0D1117 !important;
    border-top: 1px solid rgba(255, 255, 255, 0.2) !important;
    opacity: 1 !important;
}

[data-bs-theme="dark"] #notification-container .alert {
    background-color: #0D1117 !important;
    color: #e6eefA !important;
    opacity: 1 !important;
}

[data-bs-theme="dark"] .alert-success {
    background-color: rgba(14, 207, 211, 0.15) !important;
    border-color: var(--success) !important;
    color: var(--success) !important;
}

[data-bs-theme="dark"] .alert-danger {
    background-color: rgba(255, 90, 105, 0.15) !important;
    border-color: var(--danger) !important;
    color: var(--danger) !important;
}

[data-bs-theme="dark"] .alert-warning {
    background-color: rgba(255, 185, 48, 0.15) !important;
    border-color: var(--warning) !important;
    color: var(--warning) !important;
}

[data-bs-theme="dark"] .alert-info {
    background-color: rgba(77, 170, 252, 0.15) !important;
    border-color: var(--info) !important;
    color: var(--info) !important;
}