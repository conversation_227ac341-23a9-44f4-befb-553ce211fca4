/* Enhanced UI Components for Wegweiser
   Modern, clean component styling for data-focused interfaces
*/

/* Cards - Glassmorphism Enhanced */
.card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: var(--glass-border-radius);
  box-shadow: var(--glass-shadow);
  margin-bottom: var(--spacer-lg);
  transition: var(--transition-base);
  overflow: hidden;
}

/* Removed hover effects per user preference */

.card-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border-bottom: 1px solid var(--glass-border);
  padding: var(--spacer-lg);
  display: flex;
  align-items: center;
}

.card-header .card-title {
  margin-bottom: 0;
  color: var(--text-primary);
  font-weight: 600;
}

.card-header i,
.card-header .material-icons-outlined {
  margin-right: 0.75rem;
  color: var(--primary);
}

.card-body {
  padding: var(--spacer-lg);
  background: transparent;
}

.card-footer {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border-top: 1px solid var(--glass-border);
  padding: var(--spacer) var(--spacer-lg);
}

/* Breadcrumbs */
.page-breadcrumb {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacer-lg);
}

.breadcrumb-title {
  font-weight: 600;
  color: var(--text-primary);
  margin-right: var(--spacer);

}

.breadcrumb-item a {
  color: var(--text-secondary);
  transition: var(--transition-base);
}

.breadcrumb-item a:hover {
  color: var(--primary);
}

.breadcrumb-item.active {
  color: var(--text-primary);
}

.breadcrumb-item+.breadcrumb-item::before {
  content: "/" !important;
  font-family: inherit !important;
}

/* Tables */
.table {
  color: var(--text-primary);
  margin-bottom: 0;
}

.table thead th {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border-bottom: 1px solid var(--glass-border);
  color: var(--text-secondary);
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
  padding: 0.75rem 1rem;
}

.table tbody td {
  padding: 1rem;
  vertical-align: middle;
  border-bottom: 1px solid var(--border-color);
}

.table tbody tr:hover {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(3px);
}

[data-bs-theme="dark"] .table thead th {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

[data-bs-theme="dark"] .table tbody tr:hover {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(3px);
}

/* Buttons - Glassmorphism Enhanced */
.btn {
  font-weight: 500;
  padding: 0.375rem 1rem;
  border-radius: var(--glass-border-radius);
  transition: var(--transition-base);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.btn-primary {
  background: rgba(var(--primary-rgb), 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(var(--primary-rgb), 0.3);
  color: white;
}

.btn-primary:hover {
  background: rgba(var(--primary-rgb), 0.9);
  border-color: var(--primary);
  transform: translateY(-1px);
  box-shadow: var(--glass-shadow), 0 4px 8px rgba(var(--primary-rgb), 0.25);
  color: white;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.btn-outline-primary {
  background: rgba(var(--primary-rgb), 0.1);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  color: var(--primary);
  border: 1px solid rgba(var(--primary-rgb), 0.3);
}

.btn-outline-primary:hover {
  background: rgba(var(--primary-rgb), 0.8);
  border-color: var(--primary);
  color: white;
  box-shadow: var(--glass-shadow);
}

/* Health bar in tables */
.health-bar {
  height: 6px;
  border-radius: 3px;
  background-color: rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

[data-bs-theme="dark"] .health-bar,
[data-bs-theme="blue"] .health-bar {
  background-color: rgba(255, 255, 255, 0.1);
}

.health-bar-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.health-excellent {
  background: linear-gradient(90deg, #00c6fb 0%, #005bea 100%);
}

.health-good {
  background: linear-gradient(90deg, #00d084 0%, #0b945a 100%);
}

.health-fair {
  background: linear-gradient(90deg, #ffc107 0%, #fd7e14 100%);
}

.health-poor {
  background: linear-gradient(90deg, #ff416c 0%, #dc3545 100%);
}

/* Dropdown Menu - Glassmorphism Enhanced */
.dropdown-menu {
  background: var(--glass-bg) !important;
  backdrop-filter: var(--glass-blur) !important;
  -webkit-backdrop-filter: var(--glass-blur) !important;
  border: 1px solid var(--glass-border) !important;
  border-radius: var(--glass-border-radius);
  box-shadow: var(--glass-shadow);
  padding: 0.5rem 0;
  min-width: 12rem;
  opacity: 1 !important;
}

[data-bs-theme="dark"] .dropdown-menu {
  background: var(--glass-bg) !important;
  border: 1px solid var(--glass-border) !important;
  opacity: 1 !important;
}

.dropdown-item {
  color: var(--text-primary);
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  opacity: 1 !important;
}

.dropdown-item:hover {
  background-color: rgba(var(--primary-rgb), 0.1);
  color: var(--primary);
  opacity: 1 !important;
}

.dropdown-divider {
  border-top: 1px solid var(--border-color);
  margin: 0.5rem 0;
}

/* Notification widget */
#notification-container {
  position: fixed;
  top: 20px;
  right: -400px;
  width: 300px;
  z-index: 9999;
  transition: right 0.5s ease-in-out;
}

#notification-container.show {
  right: 20px;
}

/* Auth page notifications - positioned differently */
.auth-notifications {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  right: auto;
  width: 400px;
  max-width: 90vw;
}

/* Show auth notifications immediately */
.auth-notifications .alert {
  margin-bottom: 0.5rem;
}

/* Enhanced notification styling - Glassmorphism */
.notification-enhanced {
  background: var(--glass-bg) !important;
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border) !important;
  border-radius: var(--glass-border-radius);
  box-shadow: var(--glass-shadow), 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 1rem 1.25rem;
  margin-bottom: 0.75rem;
  position: relative;
  overflow: hidden;
  opacity: 1 !important;
}

.notification-enhanced::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: currentColor;
  opacity: 0.8;
}

.notification-header {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.notification-icon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 0.125rem;
}

.notification-icon i {
  font-size: 1.25rem;
  color: currentColor;
  opacity: 0.9;
}

.notification-content {
  flex: 1;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  min-width: 0;
}

.notification-image {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

.notification-text {
  flex: 1;
  min-width: 0;
}

.notification-title {
  display: block;
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
  color: currentColor;
}

.notification-message {
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.4;
  color: currentColor;
  opacity: 0.9;
}

.notification-close {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  padding: 0.25rem;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.notification-close:hover {
  opacity: 1;
}

/* Notification action buttons */
.notification-actions {
  margin-top: 0.75rem;
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.notification-actions .btn {
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: all 0.2s ease;
}

.notification-actions .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Theme-specific notification colors - OPAQUE BACKGROUNDS */
.alert-success.notification-enhanced {
  background: #ffffff !important;
  color: #065a5c !important;
  border: 2px solid #06a6a9 !important;
  border-left-color: #06a6a9 !important;
  opacity: 1 !important;
}

.alert-danger.notification-enhanced {
  background: #ffffff !important;
  color: #8b1e2b !important;
  border: 2px solid #e83c4b !important;
  border-left-color: #e83c4b !important;
  opacity: 1 !important;
}

.alert-warning.notification-enhanced {
  background: #ffffff !important;
  color: #8b5a00 !important;
  border: 2px solid #ffab00 !important;
  border-left-color: #ffab00 !important;
  opacity: 1 !important;
}

.alert-info.notification-enhanced {
  background: #ffffff !important;
  color: #1a5a5e !important;
  border: 2px solid #39d0d8 !important;
  border-left-color: #39d0d8 !important;
  opacity: 1 !important;
}

.alert-primary.notification-enhanced {
  background: #ffffff !important;
  color: #1a2b8b !important;
  border: 2px solid #2851e7 !important;
  border-left-color: #2851e7 !important;
  opacity: 1 !important;
}

/* Dark theme adjustments - OPAQUE BACKGROUNDS */
[data-bs-theme="dark"] .notification-enhanced {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3), 0 2px 4px rgba(0, 0, 0, 0.2);
}

[data-bs-theme="dark"] .alert-success.notification-enhanced {
  background: #0D1117 !important;
  color: #4dd0d3 !important;
  border: 2px solid #06a6a9 !important;
  border-left-color: #06a6a9 !important;
  opacity: 1 !important;
}

[data-bs-theme="dark"] .alert-danger.notification-enhanced {
  background: #0D1117 !important;
  color: #ff6b7a !important;
  border: 2px solid #e83c4b !important;
  border-left-color: #e83c4b !important;
  opacity: 1 !important;
}

[data-bs-theme="dark"] .alert-warning.notification-enhanced {
  background: #0D1117 !important;
  color: #ffcc4d !important;
  border: 2px solid #ffab00 !important;
  border-left-color: #ffab00 !important;
  opacity: 1 !important;
}

[data-bs-theme="dark"] .alert-info.notification-enhanced {
  background: #0D1117 !important;
  color: #66e0e8 !important;
  border: 2px solid #39d0d8 !important;
  border-left-color: #39d0d8 !important;
  opacity: 1 !important;
}

[data-bs-theme="dark"] .alert-primary.notification-enhanced {
  background: #0D1117 !important;
  color: #6b8aff !important;
  border: 2px solid #2851e7 !important;
  border-left-color: #2851e7 !important;
  opacity: 1 !important;
}

.alert {
  box-shadow: var(--shadow-md);
  border-radius: var(--border-radius);
}

/* Custom background colors for dashboard */
.bg-purple {
  background-color: #6f42c1;
}

.bg-orange {
  background-color: #fd7e14;
}

.bg-indigo {
  background-color: #6610f2;
}

/* Centralized Code Block Styling */
.code-box {
  position: relative;
  background: var(--body-bg);
  border: 2px solid var(--border-color);
  border-radius: 8px;
  padding: 1rem;
  margin: 0.5rem 0;
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.code-box pre {
  position: relative;
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
  overflow-x: auto;
  font-family: 'Fira Mono', 'Consolas', 'Menlo', 'Monaco', 'Liberation Mono', monospace;
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  word-break: break-all !important;
  max-width: 100% !important;
}

.code-box code {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  color: var(--text-primary) !important;
  font-size: 0.9rem;
  line-height: 1.4;
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  word-break: break-all !important;
  display: block !important;
  max-width: 100% !important;
}

/* Copy button styling for code blocks */
.code-box .copy-button,
.code-box button[data-copy] {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: var(--body-bg);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s ease, background-color 0.2s ease;
  z-index: 2;
}

.code-box .copy-button:hover,
.code-box button[data-copy]:hover {
  opacity: 1;
  background: var(--primary);
  color: white;
}

/* Show copy button on hover */
.code-box:hover .copy-button,
.code-box:hover button[data-copy] {
  opacity: 1;
}

/* Theme-specific code block styles */
[data-bs-theme="dark"] .code-box {
  background: var(--dark);
  border-color: var(--border-dark);
}

[data-bs-theme="dark"] .code-box .copy-button,
[data-bs-theme="dark"] .code-box button[data-copy] {
  background: var(--dark);
  border-color: var(--border-dark);
}

/* Syntax highlighting improvements */
.hljs {
  background: transparent !important;
  color: var(--text-primary) !important;
}

/* Platform icons for installation sections */
.installation-section h5 i {
  color: var(--primary);
  font-size: 1.2rem;
}

/* UUID input styling */
.input-group input[readonly] {
  background-color: var(--body-bg);
  border-color: var(--border-color);
  color: var(--text-primary);
}

/* Copy button success state */
.btn-success {
  background-color: var(--success) !important;
  border-color: var(--success) !important;
  color: white !important;
}

.btn-danger {
  background-color: var(--danger) !important;
  border-color: var(--danger) !important;
  color: white !important;
}

/* Additional responsive improvements for code blocks */
@media (max-width: 768px) {
  .code-box {
    padding: 0.75rem;
    margin: 0.25rem 0;
  }

  .code-box code {
    font-size: 0.8rem !important;
  }

  .code-box .copy-button,
  .code-box button[data-copy] {
    position: static !important;
    margin-top: 0.5rem !important;
    width: 100% !important;
    display: block !important;
  }
}

/* Ensure long URLs break properly */
.code-box code {
  overflow-wrap: break-word !important;
  hyphens: auto !important;
}

/* Better handling for installation commands */
.code-box pre code {
  white-space: pre-wrap !important;
  word-break: break-word !important;
}

/* Theme-aware highlight.js styling */
[data-bs-theme="light"] .hljs {
  background: var(--body-bg) !important;
  color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .hljs {
  background: var(--dark) !important;
  color: var(--text-primary) !important;
}

/* Override highlight.js default colors for better theme integration */
[data-bs-theme="dark"] .hljs-string,
[data-bs-theme="dark"] .hljs-attr {
  color: #98d982 !important;
}

[data-bs-theme="dark"] .hljs-keyword,
[data-bs-theme="dark"] .hljs-built_in {
  color: #79c0ff !important;
}

[data-bs-theme="dark"] .hljs-comment {
  color: #8b949e !important;
}

[data-bs-theme="dark"] .hljs-number,
[data-bs-theme="dark"] .hljs-literal {
  color: #79c0ff !important;
}

/* Light theme adjustments */
[data-bs-theme="light"] .hljs-string,
[data-bs-theme="light"] .hljs-attr {
  color: #032f62 !important;
}

[data-bs-theme="light"] .hljs-keyword,
[data-bs-theme="light"] .hljs-built_in {
  color: #d73a49 !important;
}

[data-bs-theme="light"] .hljs-comment {
  color: #6a737d !important;
}

[data-bs-theme="light"] .hljs-number,
[data-bs-theme="light"] .hljs-literal {
  color: #005cc5 !important;
}