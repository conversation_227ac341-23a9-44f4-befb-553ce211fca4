<!DOCTYPE html>
<html lang="en" data-bs-theme="{{ session.get('theme', 'light') }}">

<head>
    <!-- Favicons -->
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='images/favicon-32x32.png') }}">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    {% from 'components/breadcrumb.html' import breadcrumb %}

    <title>Wegweiser | Your Intelligent Onboarding Assistant</title>

    <!-- Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">

    <!-- Font Awesome -->
    <link href="{{ url_for('static', filename='fontawesome/css/all.min.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='fontawesome/css/fontawesome.min.css') }}" rel="stylesheet">

    <!-- Google Fonts -->
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Manrope:wght@400;500;600;700&family=Noto+Sans:wght@300;400;500;600&display=swap"
        rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Material+Icons+Outlined" rel="stylesheet">

    <!-- uPlot for charts -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/uplot@1.6.31/dist/uPlot.min.css">
    <script src="https://cdn.jsdelivr.net/npm/uplot@1.6.31/dist/uPlot.iife.min.js"></script>

    <!-- Modernized CSS -->
    <link href="{{ url_for('static', filename='css/base.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/themes.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/glassmorphism.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/layout.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/components.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/logo-variants.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/gauges.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/groups.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/organisations.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/chat.css') }}" rel="stylesheet">

    <!-- DataTables - REMOVED: Not actively used, saves ~300KB payload -->
    <!-- Can be re-added when table views are reactivated -->

    <!-- Pace loading indicator -->
    <link href="{{ url_for('static', filename='css/pace.min.css') }}" rel="stylesheet">
    <script src="{{ url_for('static', filename='js/pace.min.js') }}"></script>

    <!-- Google Charts (if needed) -->
    <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>

    <!-- Add marked.js for Markdown parsing -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>

    <!-- Highlight.js for syntax highlighting -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/languages/bash.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/languages/powershell.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/languages/python.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/languages/javascript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/languages/sql.min.js"></script>

    <!-- Extra head content -->
    {% block extra_head %}{% endblock %}
</head>

<body>
    <!-- Notification Container -->
    <div id="notification-container" class="notification-container">
        {% for notification in get_all_notifications() %}
        {{ notification|safe }}
        {% endfor %}
    </div>

    <!-- App Container -->
    <div class="app-container">
        <!-- Sidebar -->
        <aside class="sidebar-wrapper">
            <div class="sidebar-header">
                <img src="{{ url_for('static', filename='images/logo1.png') }}" alt="Logo" class="sidebar-full-logo">
                <button type="button" class="sidebar-close">
                    <i class="material-icons-outlined">close</i>
                </button>
            </div>

            <div class="sidebar-nav">
                <!-- Core Navigation (most used) -->
                <div class="sidebar-section">
                    <div class="menu-label">Core</div>
                    <ul>
                        <li>
                            <a href="{{ url_for('dashboard_bp.dashboard') }}"
                                class="nav-link {% if request.endpoint == 'dashboard_bp.dashboard' %}active{% endif %}">
                                <div class="parent-icon"><i class="fas fa-home"></i></div>
                                <div class="menu-title">Dashboard</div>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('devices_bp.handle_devices') }}"
                                class="nav-link {% if request.endpoint == 'devices_bp.handle_devices' %}active{% endif %}">
                                <div class="parent-icon"><i class="fas fa-laptop"></i></div>
                                <div class="menu-title">Devices</div>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('groups_bp.groups_page') }}"
                                class="nav-link {% if request.endpoint == 'groups_bp.groups_page' %}active{% endif %}">
                                <div class="parent-icon"><i class="fas fa-users"></i></div>
                                <div class="menu-title">Groups</div>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('organisations_bp.organisations_page') }}"
                                class="nav-link {% if request.endpoint == 'organisations_bp.organisations_page' %}active{% endif %}">
                                <div class="parent-icon"><i class="fas fa-sitemap"></i></div>
                                <div class="menu-title">Organizations</div>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Tenant Management (medium priority) -->
                <div class="sidebar-section">
                    <div class="menu-label">Management</div>
                    <ul>
                        <li>
                            <a href="{{ url_for('tenant.get_tenants_overview') }}"
                                class="nav-link {% if request.endpoint == 'tenant.get_tenants_overview' %}active{% endif %}">
                                <div class="parent-icon"><i class="fas fa-chart-bar"></i></div>
                                <div class="menu-title">Tenant Overview</div>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('payments_bp.payment') }}"
                                class="nav-link {% if request.endpoint == 'payments_bp.payment' %}active{% endif %}">
                                <div class="parent-icon"><i class="fas fa-coins"></i></div>
                                <div class="menu-title">Wegcoins</div>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- User (collapsible by default in mobile view) -->
                <div class="sidebar-section">
                    <div class="menu-label section-toggle">User</div>
                    <ul
                        class="collapsible-section {% if 'profile' in request.endpoint or 'messagecentre' in request.endpoint %}expanded{% endif %}">
                        <li>
                            <a href="{{ url_for('profile_bp.profile') }}"
                                class="nav-link {% if request.endpoint == 'profile_bp.profile' %}active{% endif %}">
                                <div class="parent-icon"><i class="fas fa-user"></i></div>
                                <div class="menu-title">Profile</div>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('messagecentre_bp.messagecentre') }}"
                                class="nav-link {% if request.endpoint == 'messagecentre_bp.messagecentre' %}active{% endif %}">
                                <div class="parent-icon"><i class="fas fa-envelope"></i></div>
                                <div class="menu-title">Messages</div>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('settings_bp.settings_index') }}"
                                class="nav-link {% if request.endpoint == 'settings_bp.settings_index' %}active{% endif %}">
                                <div class="parent-icon"><i class="fas fa-cog"></i></div>
                                <div class="menu-title">Settings</div>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Help & Support (collapsible) -->
                <div class="sidebar-section">
                    <div class="menu-label section-toggle">Help & Support</div>
                    <ul
                        class="collapsible-section {% if 'quickstart' in request.endpoint or 'faq' in request.endpoint or 'tickets' in request.endpoint %}expanded{% endif %}">
                        <li>
                            <a href="{{ url_for('quickstart_bp.quickstart') }}"
                                class="nav-link {% if request.endpoint == 'quickstart_bp.quickstart' %}active{% endif %}">
                                <div class="parent-icon"><i class="fas fa-rocket"></i></div>
                                <div class="menu-title">Quick Start</div>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('faq_bp.faq') }}"
                                class="nav-link {% if request.endpoint == 'faq_bp.faq' %}active{% endif %}">
                                <div class="parent-icon"><i class="fas fa-question"></i></div>
                                <div class="menu-title">FAQ</div>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('tickets_bp.support_dashboard') }}"
                                class="nav-link {% if request.endpoint == 'tickets_bp.support_dashboard' %}active{% endif %}">
                                <div class="parent-icon"><i class="fas fa-ticket-alt"></i></div>
                                <div class="menu-title">Support</div>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Admin (only visible to admins, always collapsed by default) -->
                {% if session.get('role') == 'admin' %}
                <div class="sidebar-section admin-section">
                    <div class="menu-label section-toggle">Admin</div>
                    <ul class="collapsible-section {% if 'admin' in request.endpoint %}expanded{% endif %}">
                        <li>
                            <a href="{{ url_for('admin_bp.view_tenants') }}"
                                class="nav-link {% if request.endpoint == 'admin_bp.view_tenants' %}active{% endif %}">
                                <div class="parent-icon"><i class="fas fa-building"></i></div>
                                <div class="menu-title">Tenants</div>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('admin_bp.devices_manage') }}"
                                class="nav-link {% if request.endpoint == 'admin_bp.devices_manage' %}active{% endif %}">
                                <div class="parent-icon"><i class="fas fa-server"></i></div>
                                <div class="menu-title">Devices</div>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('admin_snippets_bp.manage_snippets') }}"
                                class="nav-link {% if request.endpoint == 'admin_snippets_bp.manage_snippets' %}active{% endif %}">
                                <div class="parent-icon"><i class="fas fa-code"></i></div>
                                <div class="menu-title">Snippets</div>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('admin_bp.view_logs') }}"
                                class="nav-link {% if request.endpoint == 'admin_bp.view_logs' %}active{% endif %}">
                                <div class="parent-icon"><i class="fas fa-file-alt"></i></div>
                                <div class="menu-title">Logs</div>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('admin_bp.logging_config') }}"
                                class="nav-link {% if request.endpoint == 'admin_bp.logging_config' %}active{% endif %}">
                                <div class="parent-icon"><i class="fas fa-cog"></i></div>
                                <div class="menu-title">Logging Config</div>
                            </a>
                        </li>
                    </ul>
                </div>
                {% endif %}
            </div>
        </aside>

        <!-- Overlay for mobile sidebar -->
        <div class="overlay" style="display: none;"></div>
        <!-- Main Content -->
        <div class="d-flex flex-column flex-grow-1">
            <!-- Header -->
            <header class="top-header">
                <nav class="navbar navbar-expand align-items-center gap-4">
                    <button class="btn-toggle">
                        <i class="material-icons-outlined">menu</i>
                    </button>

                    <div class="ms-auto d-flex align-items-center">
                        <!-- Theme Switcher Toggle -->
                        <div class="theme-switcher me-3">
                            <button class="theme-toggle-btn" id="themeToggleBtn" title="Toggle Light/Dark Theme"
                                aria-label="Toggle theme">
                                <i class="fas fa-sun"></i>
                                <i class="fas fa-moon"></i>
                            </button>
                        </div>

                        <!-- Notifications -->
                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle dropdown-toggle-nocaret position-relative"
                                data-bs-auto-close="outside" data-bs-toggle="dropdown" href="javascript:;">
                                <i class="material-icons-outlined">notifications</i>
                                <span class="badge-notify bg-danger" id="notificationCount">{{ notifications|default(0)
                                    }}</span>
                            </a>
                            <div class="dropdown-menu dropdown-menu-end shadow">
                                <div class="px-3 py-2 d-flex align-items-center justify-content-between border-bottom">
                                    <h6 class="mb-0">Notifications</h6>
                                    <div class="dropdown">
                                        <button class="btn btn-sm dropdown-toggle dropdown-toggle-nocaret" type="button"
                                            data-bs-toggle="dropdown">
                                            <i class="material-icons-outlined">more_vert</i>
                                        </button>
                                        <div class="dropdown-menu">
                                            <a class="dropdown-item" href="javascript:;">Mark all as read</a>
                                            <div class="dropdown-divider"></div>
                                            <a class="dropdown-item" href="javascript:;">Settings</a>
                                        </div>
                                    </div>
                                </div>
                                <div id="notificationList" style="max-height: 300px; overflow-y: auto;">
                                    <!-- Notifications will be dynamically inserted here -->
                                    <div class="dropdown-item d-flex align-items-center py-2">
                                        <div class="me-3">
                                            <i class="fas fa-info-circle text-primary"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-1 fw-normal">Welcome to Wegweiser</h6>
                                            <p class="mb-0 text-muted small">Get started by adding your first device</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-2 border-top text-center">
                                    <a href="#" class="small">View All Notifications</a>
                                </div>
                            </div>
                        </div>

                        <!-- Help -->
                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle dropdown-toggle-nocaret position-relative"
                                data-bs-toggle="dropdown" href="javascript:;">
                                <i class="material-icons-outlined">help_outline</i>
                            </a>
                            <div class="dropdown-menu dropdown-menu-end shadow">
                                <a class="dropdown-item" href="{{ url_for('quickstart_bp.quickstart') }}">
                                    <i class="fas fa-rocket me-2"></i> Quick Start
                                </a>
                                <a class="dropdown-item" href="{{ url_for('faq_bp.faq') }}">
                                    <i class="fas fa-question-circle me-2"></i> FAQ
                                </a>
                                <a class="dropdown-item" href="{{ url_for('tickets_bp.support_dashboard') }}">
                                    <i class="fas fa-ticket-alt me-2"></i> Support
                                </a>
                            </div>
                        </div>

                        <!-- User Profile -->
                        <div class="nav-item dropdown ms-3">
                            <a href="javascript:;" class="dropdown-toggle dropdown-toggle-nocaret"
                                data-bs-toggle="dropdown">
                                <img src="{{ url_for('static', filename=user.profile_picture or 'images/profilepictures/default.png') }}"
                                    class="user-profile rounded-circle" alt="User">
                            </a>
                            <div class="dropdown-menu dropdown-menu-end shadow">
                                <div class="dropdown-item text-center">
                                    <img src="{{ url_for('static', filename=user.profile_picture or 'images/profilepictures/default.png') }}"
                                        class="rounded-circle mb-3" width="80" height="80" alt="User">
                                    <h6 class="mb-0 fw-bold">Hello, {{ session['userfirstname'] }}</h6>
                                    <p class="mb-0 text-muted small">{{ session.get('userrole', 'User') }}</p>
                                </div>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item d-flex align-items-center"
                                    href="{{ url_for('profile_bp.profile') }}">
                                    <i class="material-icons-outlined me-2">person_outline</i> Profile
                                </a>
                                <a class="dropdown-item d-flex align-items-center"
                                    href="{{ url_for('settings_bp.settings_index') }}">
                                    <i class="material-icons-outlined me-2">settings</i> Settings
                                </a>
                                <a class="dropdown-item d-flex align-items-center"
                                    href="{{ url_for('dashboard_bp.dashboard') }}">
                                    <i class="material-icons-outlined me-2">dashboard</i> Dashboard
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item d-flex align-items-center"
                                    href="{{ url_for('login_bp.logout') }}">
                                    <i class="material-icons-outlined me-2">power_settings_new</i> Logout
                                </a>
                            </div>
                        </div>
                    </div>
                </nav>
            </header>

            <!-- Main Wrapper -->
            <main class="main-wrapper">
                <div class="main-content">
                    {% block content %}{% endblock %}
                </div>

                <!-- Footer -->
                <footer class="page-footer">
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-12 text-center text-md-end">
                                <p class="mb-0 footer-text"><span class="fw-light">&copy; {{
                                        current_year|default('2025') }}</span> <span class="fw-medium">Wegweiser</span>
                                    <span class="footer-separator">|</span> <span class="fw-light">All rights
                                        reserved</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </footer>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz"
        crossorigin="anonymous"></script>

    <!-- Core JS -->
    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('static', filename='js/theme-switcher.js') }}"></script>

    <!-- Notifications JS -->
    <script src="{{ url_for('static', filename='js/notifications.js') }}"></script>

    <!-- Centralized Utilities -->
    <script src="{{ url_for('static', filename='js/common/copy-utility.js') }}"></script>
    <script src="{{ url_for('static', filename='js/common/highlight-utility.js') }}"></script>

    <!-- <script src="{{ url_for('static', filename='js/health-gauge.js') }}"></script> -->

    <!-- DataTables JS - REMOVED: Not actively used, saves ~300KB+ payload -->
    <!-- Can be re-added when table views are reactivated:
    <script src="{{ url_for('static', filename='plugins/datatable/js/jquery.dataTables.min.js') }}"></script>
    <script src="{{ url_for('static', filename='plugins/datatable/js/dataTables.bootstrap5.min.js') }}"></script>
    <script src="https://cdn.datatables.net/buttons/2.3.6/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.3.6/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.3.6/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.3.6/js/buttons.print.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.3.6/js/buttons.colVis.min.js"></script>
    -->

    <!-- Extra scripts specific to each page -->
    {% block extra_scripts %}{% endblock %}

    <!-- Global scripts -->
    {% block global_scripts %}{% endblock %}
    {% block modals %}{% endblock %}

    <!-- Theme Toggle Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const themeToggleBtn = document.getElementById('themeToggleBtn');

            if (themeToggleBtn) {
                themeToggleBtn.addEventListener('click', function () {
                    // Get current theme
                    const currentTheme = document.documentElement.getAttribute('data-bs-theme') || 'light';
                    // Toggle theme
                    const newTheme = currentTheme === 'light' ? 'dark' : 'light';

                    // Use the global setTheme function from theme-switcher.js
                    if (typeof window.setTheme === 'function') {
                        window.setTheme(newTheme);
                    } else {
                        // Fallback if global function not available
                        document.documentElement.setAttribute('data-bs-theme', newTheme);
                    }

                    // Use the global sendThemeToServer function if available
                    if (typeof window.sendThemeToServer === 'function') {
                        window.sendThemeToServer(newTheme);
                    }

                    // Save to localStorage
                    localStorage.setItem('theme', newTheme);
                });
            }
        });
    </script>
</body>

</html>